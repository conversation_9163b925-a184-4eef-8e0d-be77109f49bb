import requests
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import json
import os
import base64
from urllib.parse import urlparse

# محاولة استيراد pyperclip مع معالجة الخطأ
try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("تحذير: pyperclip غير مثبت. استخدم: pip install pyperclip")

class MatchScraperApp:
    def __init__(self, root):
        self.root = root
        self.root.title("مولد جدول المباريات - يلا كورة مع App Creator 24")
        self.matches = []
        self.last_html = ""  # لحفظ HTML الأخير
        self.app_channels = []  # قنوات التطبيق
        self.logos_cache = {}  # كاش اللوجوهات

        # تحميل قنوات التطبيق
        self.load_app_channels()

        # إطار التاريخ والقنوات
        top_frame = tk.Frame(root)
        top_frame.pack(fill='x', padx=10, pady=5)

        # التاريخ
        date_frame = tk.Frame(top_frame)
        date_frame.pack(side='left', fill='x', expand=True)

        self.date_label = tk.Label(date_frame, text="اختر التاريخ (YYYY-MM-DD):")
        self.date_label.pack()
        self.date_entry = tk.Entry(date_frame)
        self.date_entry.insert(0, datetime.date.today().strftime("%Y-%m-%d"))
        self.date_entry.pack()

        # إدارة القنوات
        channels_frame = tk.Frame(top_frame)
        channels_frame.pack(side='right', padx=20)

        tk.Label(channels_frame, text="إدارة قنوات التطبيق:", font=('Arial', 10, 'bold')).pack()

        channels_btn_frame = tk.Frame(channels_frame)
        channels_btn_frame.pack()

        self.load_channels_btn = tk.Button(channels_btn_frame, text="📂 تحميل القنوات",
                                          command=self.load_app_channels_dialog)
        self.load_channels_btn.pack(side='left', padx=2)

        self.manage_channels_btn = tk.Button(channels_btn_frame, text="⚙️ إدارة القنوات",
                                           command=self.manage_channels_dialog)
        self.manage_channels_btn.pack(side='left', padx=2)

        self.create_rayaa_btn = tk.Button(channels_btn_frame, text="🏆 إنشاء قنوات راية كافية",
                                        command=self.create_rayaa_channels)
        self.create_rayaa_btn.pack(side='left', padx=2)

        # زر تحميل المباريات
        self.fetch_button = tk.Button(root, text="تحميل المباريات", command=self.fetch_matches)
        self.fetch_button.pack(pady=5)

        # شجرة عرض المباريات
        self.tree = ttk.Treeview(root, columns=("team1", "team2", "time", "original_channel", "app_channel"), show="headings")

        # تعيين عناوين الأعمدة
        self.tree.heading("team1", text="الفريق 1")
        self.tree.heading("team2", text="الفريق 2")
        self.tree.heading("time", text="الساعة")
        self.tree.heading("original_channel", text="القناة الأصلية")
        self.tree.heading("app_channel", text="قناة التطبيق")

        # تعيين عرض الأعمدة
        self.tree.column("team1", width=150)
        self.tree.column("team2", width=150)
        self.tree.column("time", width=80)
        self.tree.column("original_channel", width=120)
        self.tree.column("app_channel", width=120)

        self.tree.pack(fill=tk.BOTH, expand=True)

        # ربط النقر المزدوج لتعديل قناة التطبيق
        self.tree.bind('<Double-1>', self.edit_app_channel)

        # تلميح للمستخدم
        tip_frame = tk.Frame(root)
        tip_frame.pack(fill='x', pady=5)

        tip_label = tk.Label(tip_frame,
                           text="💡 نقر مزدوج على عمود 'قناة التطبيق' لتغيير القناة | 🏆 استخدم 'إنشاء قنوات راية كافية' لإنشاء ملف القنوات",
                           fg="orange", font=('Arial', 9), wraplength=600)
        tip_label.pack()

        # تلميح للمستخدم
        tip_label = tk.Label(root, text="💡 نقر مزدوج على عمود 'قناة التطبيق' لتغيير القناة",
                           fg="orange", font=('Arial', 9))
        tip_label.pack(pady=5)

        # أزرار
        button_frame = tk.Frame(root)
        button_frame.pack(pady=10)

        self.generate_button = tk.Button(button_frame, text="🎉 توليد كود HTML", command=self.generate_html)
        self.generate_button.pack(side=tk.LEFT, padx=5)

        self.copy_button = tk.Button(
            button_frame,
            text="📋 نسخ الكود",
            command=self.copy_html_code,
            state=tk.NORMAL if PYPERCLIP_AVAILABLE else tk.DISABLED
        )
        self.copy_button.pack(side=tk.LEFT, padx=5)

        if not PYPERCLIP_AVAILABLE:
            # إضافة تلميح للمستخدم
            tk.Label(root, text="💡 لتفعيل النسخ: pip install pyperclip", fg="orange").pack()

        self.debug_button = tk.Button(button_frame, text="🔍 حفظ HTML للتحقق", command=self.save_debug_html)
        self.debug_button.pack(side=tk.LEFT, padx=5)

    def fetch_matches(self):
        date_str = self.date_entry.get()
        try:
            parsed_date = datetime.datetime.strptime(date_str, "%Y-%m-%d")
            # تنسيق التاريخ بدون الصفر في المقدمة
            month = parsed_date.month
            day = parsed_date.day
            year = parsed_date.year
            url = f"https://www.yallakora.com/match-center?date={month}/{day}/{year}#days"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers)
            print(f"Status Code: {response.status_code}")
            print(f"URL: {url}")

            self.last_html = response.text  # حفظ HTML للتحقق
            soup = BeautifulSoup(response.text, 'html.parser')
            self.matches = []
            self.tree.delete(*self.tree.get_children())

            # محاولة عدة طرق لاستخراج المباريات
            matches_found = False

            # البحث عن المباريات بناءً على الهيكل الصحيح
            matches_section = soup.select('.item.future.liItem')
            print(f"Found {len(matches_section)} match items")

            for match_item in matches_section:
                try:
                    # استخراج أسماء الفرق
                    team_a = match_item.select_one('.teams.teamA p')
                    team_b = match_item.select_one('.teams.teamB p')

                    # استخراج الوقت
                    time_element = match_item.select_one('.MResult .time')

                    # استخراج القناة
                    channel_element = match_item.select_one('.channel')

                    if team_a and team_b and time_element:
                        team1 = team_a.text.strip()
                        team2 = team_b.text.strip()
                        match_time = time_element.text.strip()
                        channel = channel_element.text.strip() if channel_element else ""

                        if team1 and team2 and match_time:
                            # استخراج اللوجوهات
                            logo1_url = self.extract_team_logo(match_item, 'teamA')
                            logo2_url = self.extract_team_logo(match_item, 'teamB')

                            # اقتراح قناة من التطبيق
                            suggested_app_channel = self.suggest_app_channel(channel)

                            match_data = {
                                "team1": team1,
                                "team2": team2,
                                "time": match_time,
                                "original_channel": channel,
                                "app_channel": suggested_app_channel,
                                "logo1": logo1_url,
                                "logo2": logo2_url
                            }

                            self.matches.append(match_data)
                            self.tree.insert("", "end", values=(team1, team2, match_time, channel, suggested_app_channel))
                            matches_found = True
                            print(f"Added match: {team1} vs {team2} at {match_time}")

                except Exception as e:
                    print(f"Error processing match item: {e}")
                    continue



            if not matches_found:
                messagebox.showwarning("لا توجد مباريات", f"لم يتم العثور على مباريات في تاريخ {date_str}")
            else:
                messagebox.showinfo("تم بنجاح", f"تم تحميل {len(self.matches)} مباراة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء جلب المباريات:\n{e}")
            print(f"Error details: {e}")

    def extract_team_logo(self, match_item, team_class):
        """استخراج لوجو الفريق"""
        try:
            img_element = match_item.select_one(f'.teams.{team_class} img')
            if img_element:
                logo_url = img_element.get('data-src') or img_element.get('src')
                if logo_url and not logo_url.startswith('data:'):
                    # تحويل الرابط النسبي إلى مطلق
                    if logo_url.startswith('//'):
                        logo_url = 'https:' + logo_url
                    elif logo_url.startswith('/'):
                        logo_url = 'https://www.yallakora.com' + logo_url
                    return logo_url
        except Exception as e:
            print(f"Error extracting logo for {team_class}: {e}")
        return ""

    def suggest_app_channel(self, original_channel):
        """اقتراح قناة من تطبيق راية كافية بناءً على القناة الأصلية"""
        # قنوات راية كافية المتاحة
        rayaa_channels = {
            "beIN Sports 1": ["بي إن سبورت", "bein", "BeIN", "بين سبورت"],
            "beIN Sports 2": ["بي إن سبورت", "bein", "BeIN", "بين سبورت"],
            "beIN Sports 3": ["بي إن سبورت", "bein", "BeIN", "بين سبورت"],
            "ON Time Sports 1": ["أون تايم", "on time", "ON Time", "إم بى س مصر"],
            "ON Time Sports 2": ["أون تايم", "on time", "ON Time", "إم بى س مصر"],
            "SSC Sports 1": ["SSC", "ssc", "الرياضية السعودية"],
            "SSC Sports 2": ["SSC", "ssc", "الرياضية السعودية"],
            "Al Ahly TV": ["الأهلي", "ahly", "Al Ahly"],
            "Zamalek TV": ["الزمالك", "zamalek", "Zamalek"]
        }

        if not original_channel:
            return "beIN Sports 1"  # افتراضي

        original_lower = original_channel.lower()

        # البحث في قنوات راية كافية
        for channel_name, keywords in rayaa_channels.items():
            for keyword in keywords:
                if keyword.lower() in original_lower:
                    return channel_name

        # البحث في قنوات التطبيق المحملة إن وجدت
        if self.app_channels:
            for app_channel in self.app_channels:
                app_name_lower = app_channel['name'].lower()
                if original_lower in app_name_lower or app_name_lower in original_lower:
                    return app_channel['name']
            return self.app_channels[0]['name']

        # افتراضي
        return "beIN Sports 1"

    def load_app_channels(self):
        """تحميل قنوات التطبيق من ملف data.json"""
        try:
            if os.path.exists("data.json"):
                with open("data.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.app_channels = data.get("channels", [])
                    print(f"تم تحميل {len(self.app_channels)} قناة من التطبيق")
            else:
                print("ملف data.json غير موجود")
                self.app_channels = []
        except Exception as e:
            print(f"خطأ في تحميل قنوات التطبيق: {e}")
            self.app_channels = []

    def load_app_channels_dialog(self):
        """نافذة تحميل قنوات التطبيق"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف data.json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.app_channels = data.get("channels", [])
                    messagebox.showinfo("تم التحميل", f"تم تحميل {len(self.app_channels)} قناة من التطبيق")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الملف:\n{e}")

    def manage_channels_dialog(self):
        """نافذة إدارة قنوات التطبيق"""
        if not self.app_channels:
            messagebox.showwarning("تحذير", "لا توجد قنوات محملة!\nيرجى تحميل ملف data.json أولاً")
            return

        # إنشاء نافذة جديدة
        channels_window = tk.Toplevel(self.root)
        channels_window.title("إدارة قنوات التطبيق")
        channels_window.geometry("600x400")

        # قائمة القنوات
        tk.Label(channels_window, text="قنوات التطبيق المتاحة:", font=('Arial', 12, 'bold')).pack(pady=10)

        # إطار القائمة
        list_frame = tk.Frame(channels_window)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قائمة مع شريط تمرير
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')

        channels_listbox = tk.Listbox(list_frame, font=('Arial', 10), yscrollcommand=scrollbar.set)
        channels_listbox.pack(fill='both', expand=True)
        scrollbar.config(command=channels_listbox.yview)

        # ملء القائمة
        for i, channel in enumerate(self.app_channels):
            channels_listbox.insert(tk.END, f"{i+1}. {channel['name']}")

        # معلومات القناة المختارة
        info_frame = tk.Frame(channels_window)
        info_frame.pack(fill='x', padx=20, pady=10)

        info_text = tk.Text(info_frame, height=4, font=('Arial', 9))
        info_text.pack(fill='x')

        def show_channel_info(event):
            selection = channels_listbox.curselection()
            if selection:
                channel = self.app_channels[selection[0]]
                info_text.delete(1.0, tk.END)
                info_text.insert(tk.END, f"الاسم: {channel['name']}\n")
                info_text.insert(tk.END, f"رابط البث: {channel.get('m3u8_url', 'غير محدد')}\n")
                info_text.insert(tk.END, f"رابط التفعيل: {channel.get('activation_base_url', 'غير محدد')}\n")
                info_text.insert(tk.END, f"عداد البداية: {channel.get('start_counter', 'غير محدد')}")

        channels_listbox.bind('<<ListboxSelect>>', show_channel_info)

    def edit_app_channel(self, event):
        """تعديل قناة التطبيق للمباراة المختارة"""
        selection = self.tree.selection()
        if not selection:
            return

        item = selection[0]
        col = self.tree.identify_column(event.x)

        # التحقق من النقر على عمود قناة التطبيق (العمود الخامس)
        if col == '#5':
            if not self.app_channels:
                messagebox.showwarning("تحذير", "لا توجد قنوات تطبيق محملة!\nيرجى تحميل ملف data.json أولاً")
                return

            # إنشاء نافذة اختيار القناة
            channel_window = tk.Toplevel(self.root)
            channel_window.title("اختيار قناة التطبيق")
            channel_window.geometry("400x300")
            channel_window.transient(self.root)
            channel_window.grab_set()

            tk.Label(channel_window, text="اختر قناة التطبيق:", font=('Arial', 12, 'bold')).pack(pady=10)

            # قائمة القنوات
            listbox_frame = tk.Frame(channel_window)
            listbox_frame.pack(fill='both', expand=True, padx=20, pady=10)

            scrollbar = ttk.Scrollbar(listbox_frame)
            scrollbar.pack(side='right', fill='y')

            channels_listbox = tk.Listbox(listbox_frame, font=('Arial', 10), yscrollcommand=scrollbar.set)
            channels_listbox.pack(fill='both', expand=True)
            scrollbar.config(command=channels_listbox.yview)

            # ملء القائمة
            for channel in self.app_channels:
                channels_listbox.insert(tk.END, channel['name'])

            # أزرار
            buttons_frame = tk.Frame(channel_window)
            buttons_frame.pack(pady=10)

            def apply_channel():
                selection = channels_listbox.curselection()
                if selection:
                    selected_channel = self.app_channels[selection[0]]['name']
                    # تحديث الجدول
                    values = list(self.tree.item(item, 'values'))
                    values[4] = selected_channel
                    self.tree.item(item, values=values)

                    # تحديث البيانات
                    row_index = self.tree.index(item)
                    if row_index < len(self.matches):
                        self.matches[row_index]['app_channel'] = selected_channel

                    channel_window.destroy()
                else:
                    messagebox.showwarning("تحذير", "يرجى اختيار قناة")

            tk.Button(buttons_frame, text="تطبيق", command=apply_channel, bg='#4CAF50', fg='white').pack(side='left', padx=5)
            tk.Button(buttons_frame, text="إلغاء", command=channel_window.destroy, bg='#f44336', fg='white').pack(side='left', padx=5)

            # تحديد القناة الحالية إن وجدت
            current_channel = self.tree.item(item, 'values')[4]
            for i, channel in enumerate(self.app_channels):
                if channel['name'] == current_channel:
                    channels_listbox.selection_set(i)
                    channels_listbox.see(i)
                    break

    def generate_html(self):
        if not self.matches:
            messagebox.showwarning("تحذير", "لا توجد مباريات لتوليد الكود!")
            return

        # تحديث بيانات القنوات من الجدول
        for i, item in enumerate(self.tree.get_children()):
            values = self.tree.item(item, 'values')
            if i < len(self.matches):
                self.matches[i]["original_channel"] = values[3]
                self.matches[i]["app_channel"] = values[4]

        html = self.generate_advanced_html()

        file_path = filedialog.asksaveasfilename(
            defaultextension=".html",
            filetypes=[("HTML Files", "*.html")],
            initialvalue=f"matches_{datetime.date.today().strftime('%Y-%m-%d')}.html"
        )
        if file_path:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(html)
            messagebox.showinfo("تم الحفظ", "تم توليد ملف HTML منسق بنجاح!")

    def generate_advanced_html(self):
        """توليد HTML متقدم بنفس تصميم تطبيق راية كافية"""
        date_str = self.date_entry.get()

        html = f"""<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>جدول مباريات {date_str} - راية كافية</title>
  <style>
    body {{
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url('https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?auto=format&fit=crop&w=1050&q=80');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      color: #ffffff;
    }}

    .overlay {{
      background-color: rgba(0, 0, 0, 0.75);
      padding: 20px;
      min-height: 100vh;
    }}

    .title {{
      text-align: center;
      color: #0EA691;
      font-size: 28px;
      margin-bottom: 10px;
    }}

    .subtitle {{
      text-align: center;
      color: #0EA691;
      font-size: 18px;
      margin-bottom: 20px;
      opacity: 0.8;
    }}

    .top-buttons {{
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }}

    .top-buttons button {{
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      background-color: #0EA691;
      color: #fff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s;
    }}

    .top-buttons button:hover {{
      background-color: #088e7a;
    }}

    .search-box {{
      text-align: center;
      margin-bottom: 20px;
    }}

    .search-input {{
      padding: 10px;
      width: 80%;
      max-width: 300px;
      border-radius: 6px;
      border: none;
      outline: none;
    }}

    .section-title {{
      color: #0EA691;
      font-size: 20px;
      margin: 30px 10px 15px;
      text-align: center;
    }}

    .matches-grid {{
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 15px;
      padding: 10px;
      max-width: 1200px;
      margin: 0 auto;
    }}

    .match-card-link {{
      text-decoration: none;
      color: inherit;
      display: block;
    }}

    .match-card {{
      background-color: #2a2a2a;
      border-radius: 15px;
      box-shadow: 0 0 12px #0ea691;
      overflow: hidden;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      cursor: pointer;
      position: relative;
    }}

    .match-card:hover {{
      transform: scale(1.05);
      box-shadow: 0 0 25px #0ea691;
      border-color: #0EA691;
    }}

    .match-card::after {{
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(14, 166, 145, 0.1), rgba(14, 166, 145, 0.05));
      opacity: 0;
      transition: opacity 0.3s ease;
    }}

    .match-card:hover::after {{
      opacity: 1;
    }}

    .match-header {{
      background: linear-gradient(135deg, #0EA691, #088e7a);
      padding: 15px;
      text-align: center;
    }}

    .match-time {{
      font-size: 18px;
      font-weight: bold;
      color: white;
      margin-bottom: 5px;
    }}

    .match-date {{
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }}

    .teams-container {{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
    }}

    .team {{
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
    }}

    .team-logo {{
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #0EA691;
      margin-bottom: 10px;
      background: rgba(255, 255, 255, 0.1);
    }}

    .no-logo {{
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #0EA691, #088e7a);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
      border: 3px solid #0EA691;
      margin-bottom: 10px;
    }}

    .team-name {{
      font-weight: bold;
      font-size: 14px;
      text-align: center;
      color: #0EA691;
      max-width: 100px;
      word-wrap: break-word;
    }}

    .vs {{
      font-size: 24px;
      font-weight: bold;
      color: #0EA691;
      margin: 0 15px;
    }}

    .channel-info {{
      background: rgba(14, 166, 145, 0.1);
      padding: 15px;
      border-top: 1px solid #0EA691;
    }}

    .original-channel {{
      font-size: 12px;
      color: #ccc;
      margin-bottom: 8px;
      text-align: center;
    }}

    .app-channel {{
      background-color: #0EA691;
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      font-weight: bold;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: block;
    }}

    .app-channel:hover {{
      background-color: #088e7a;
      transform: scale(1.05);
    }}

    .watch-now {{
      padding: 15px;
      text-align: center;
      background: linear-gradient(135deg, #0EA691, #088e7a);
      margin-top: 10px;
    }}

    .watch-btn {{
      color: white;
      font-weight: bold;
      font-size: 16px;
      padding: 8px 0;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }}

    .match-card:hover .watch-btn {{
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }}

    .stats {{
      text-align: center;
      margin: 30px 0;
      padding: 20px;
      background: rgba(14, 166, 145, 0.1);
      border-radius: 10px;
      max-width: 600px;
      margin: 30px auto;
    }}

    .stats h3 {{
      color: #0EA691;
      margin-bottom: 15px;
    }}

    .stats-grid {{
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
    }}

    .stat-item {{
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #0EA691;
    }}

    .stat-number {{
      font-size: 24px;
      font-weight: bold;
      color: #0EA691;
    }}

    .stat-label {{
      font-size: 12px;
      color: #ccc;
      margin-top: 5px;
    }}

    footer {{
      text-align: center;
      color: #0EA691;
      margin-top: 40px;
      font-size: 14px;
      padding: 20px;
      border-top: 1px solid #0EA691;
    }}

    @media (max-width: 768px) {{
      .matches-grid {{
        grid-template-columns: 1fr;
        padding: 5px;
      }}

      .teams-container {{
        flex-direction: column;
        gap: 15px;
      }}

      .vs {{
        transform: rotate(90deg);
        margin: 10px 0;
      }}

      .team {{
        width: 100%;
      }}
    }}
  </style>
</head>
<body>
  <div class="overlay">
    <h2 class="title">📺 راية كافية</h2>
    <div class="subtitle">جدول مباريات {date_str}</div>

    <div class="top-buttons">
      <button onclick="downloadPlayer()">📥 تنزيل المشغل</button>
      <button onclick="shareApp()">🔗 مشاركة التطبيق</button>
      <button onclick="refreshMatches()">🔄 تحديث المباريات</button>
    </div>

    <div class="search-box">
      <input type="text" id="search" class="search-input" placeholder="ابحث عن مباراة أو فريق...">
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats">
      <h3>📊 إحصائيات اليوم</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{len(self.matches)}</div>
          <div class="stat-label">إجمالي المباريات</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{len(set(match.get('app_channel', '') for match in self.matches if match.get('app_channel')))}</div>
          <div class="stat-label">القنوات المتاحة</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{len(set(match.get('original_channel', '') for match in self.matches if match.get('original_channel')))}</div>
          <div class="stat-label">القنوات الأصلية</div>
        </div>
      </div>
    </div>

    <h3 class="section-title">🏆 مباريات اليوم</h3>
    <div class="matches-grid" id="matches">"""

        for match in self.matches:
            # تحديد اللوجو أو الحرف الأول
            logo1_html = self.get_rayaa_logo_html(match.get('logo1', ''), match['team1'])
            logo2_html = self.get_rayaa_logo_html(match.get('logo2', ''), match['team2'])

            # تحديد رابط القناة
            channel_link = self.get_channel_link(match.get('app_channel', ''))

            html += f"""
      <a href="{channel_link}" class="match-card-link">
        <div class="match-card" data-team1="{match['team1'].lower()}" data-team2="{match['team2'].lower()}">
          <div class="match-header">
            <div class="match-time">⏰ {match['time']}</div>
            <div class="match-date">📅 {date_str}</div>
          </div>

          <div class="teams-container">
            <div class="team">
              {logo1_html}
              <div class="team-name">{match['team1']}</div>
            </div>
            <div class="vs">VS</div>
            <div class="team">
              {logo2_html}
              <div class="team-name">{match['team2']}</div>
            </div>
          </div>

          <div class="channel-info">
            <div class="original-channel">📺 القناة الأصلية: {match.get('original_channel', 'غير محدد')}</div>
            <div class="app-channel">
              📱 شاهد على {match.get('app_channel', 'غير محدد')}
            </div>
          </div>

          <div class="watch-now">
            <div class="watch-btn">🎬 شاهد الآن</div>
          </div>
        </div>
      </a>"""

        html += f"""
    </div>

    <footer>
      <p><strong>جميع الحقوق محفوظة © Rayaa Cafe <script>document.write(new Date().getFullYear());</script></strong></p>
      <p>🔥 تم إنشاؤه بواسطة مولد جدول المباريات - يلا كورة</p>
      <p>⚡ عدد المباريات: {len(self.matches)} | آخر تحديث: {datetime.datetime.now().strftime('%H:%M')}</p>
    </footer>
  </div>

  <script>
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', () => {{
      const query = searchInput.value.toLowerCase();
      const cardLinks = document.querySelectorAll('.match-card-link');
      cardLinks.forEach(cardLink => {{
        const card = cardLink.querySelector('.match-card');
        const team1 = card.getAttribute('data-team1');
        const team2 = card.getAttribute('data-team2');
        const isVisible = team1.includes(query) || team2.includes(query);
        cardLink.style.display = isVisible ? 'block' : 'none';
      }});
    }});

    function downloadPlayer() {{
      window.open("https://play.google.com/store/apps/details?id=com.ytv.pronew", "_blank");
    }}

    function shareApp() {{
      const shareText = "📺 جرّب الآن تطبيق راية كافية!\\n\\nشاهد مباريات اليوم:\\nhttps://your_app_link.com";
      if (navigator.share) {{
        navigator.share({{
          title: "مباريات اليوم - راية كافية",
          text: shareText,
          url: window.location.href
        }});
      }} else {{
        navigator.clipboard.writeText(shareText).then(() => {{
          alert("تم نسخ الرابط! شاركه مع أصدقائك");
        }});
      }}
    }}

    function refreshMatches() {{
      window.location.reload();
    }}

    // تأثيرات الحركة
    document.addEventListener('DOMContentLoaded', function() {{
      const cardLinks = document.querySelectorAll('.match-card-link');
      cardLinks.forEach((cardLink, index) => {{
        cardLink.style.opacity = '0';
        cardLink.style.transform = 'translateY(20px)';
        setTimeout(() => {{
          cardLink.style.transition = 'all 0.5s ease';
          cardLink.style.opacity = '1';
          cardLink.style.transform = 'translateY(0)';
        }}, index * 100);
      }});

      // إضافة تأثير النقر
      cardLinks.forEach(cardLink => {{
        let pressTimer;

        cardLink.addEventListener('click', function(e) {{
          const card = this.querySelector('.match-card');
          card.style.transform = 'scale(0.95)';
          setTimeout(() => {{
            card.style.transform = '';
          }}, 150);
        }});

        // إضافة تأثير الضغط المطول لإظهار معلومات إضافية
        cardLink.addEventListener('mousedown', function(e) {{
          pressTimer = setTimeout(() => {{
            const team1 = this.querySelector('.team-name').textContent;
            const team2 = this.querySelectorAll('.team-name')[1].textContent;
            const time = this.querySelector('.match-time').textContent;
            const channel = this.querySelector('.app-channel').textContent;

            alert(`📋 معلومات المباراة:\\n\\n🏆 ${team1} ضد ${team2}\\n${time}\\n${channel}\\n\\n💡 انقر للمشاهدة المباشرة!`);
          }}, 1000);
        }});

        cardLink.addEventListener('mouseup', function() {{
          clearTimeout(pressTimer);
        }});

        cardLink.addEventListener('mouseleave', function() {{
          clearTimeout(pressTimer);
        }});
      }});
    }});
  </script>
</body>
</html>"""

        return html

    def get_rayaa_logo_html(self, logo_url, team_name):
        """توليد HTML للوجو بتصميم راية كافية"""
        if logo_url and logo_url.strip():
            return f'<img src="{logo_url}" alt="{team_name}" class="team-logo" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\'"><div class="no-logo" style="display:none">{team_name[0] if team_name else "?"}</div>'
        else:
            return f'<div class="no-logo">{team_name[0] if team_name else "?"}</div>'

    def get_channel_link(self, channel_name):
        """توليد رابط القناة بناءً على اسمها"""
        if not channel_name or channel_name == "غير محدد":
            return "#"

    def create_rayaa_channels(self):
        """إنشاء ملف قنوات راية كافية"""
        rayaa_data = {
            "daily_code": "RAYAA2024",
            "channels": [
                {
                    "name": "beIN Sports 1",
                    "m3u8_url": "https://example.com/bein1.m3u8",
                    "activation_base_url": "https://example.com/activate1.js?",
                    "start_counter": 1000
                },
                {
                    "name": "beIN Sports 2",
                    "m3u8_url": "https://example.com/bein2.m3u8",
                    "activation_base_url": "https://example.com/activate2.js?",
                    "start_counter": 2000
                },
                {
                    "name": "beIN Sports 3",
                    "m3u8_url": "https://example.com/bein3.m3u8",
                    "activation_base_url": "https://example.com/activate3.js?",
                    "start_counter": 3000
                },
                {
                    "name": "ON Time Sports 1",
                    "m3u8_url": "https://example.com/on1.m3u8",
                    "activation_base_url": "https://example.com/activate4.js?",
                    "start_counter": 4000
                },
                {
                    "name": "ON Time Sports 2",
                    "m3u8_url": "https://example.com/on2.m3u8",
                    "activation_base_url": "https://example.com/activate5.js?",
                    "start_counter": 5000
                },
                {
                    "name": "SSC Sports 1",
                    "m3u8_url": "https://example.com/ssc1.m3u8",
                    "activation_base_url": "https://example.com/activate6.js?",
                    "start_counter": 6000
                },
                {
                    "name": "SSC Sports 2",
                    "m3u8_url": "https://example.com/ssc2.m3u8",
                    "activation_base_url": "https://example.com/activate7.js?",
                    "start_counter": 7000
                },
                {
                    "name": "Al Ahly TV",
                    "m3u8_url": "https://example.com/ahly.m3u8",
                    "activation_base_url": "https://example.com/activate8.js?",
                    "start_counter": 8000
                },
                {
                    "name": "Zamalek TV",
                    "m3u8_url": "https://example.com/zamalek.m3u8",
                    "activation_base_url": "https://example.com/activate9.js?",
                    "start_counter": 9000
                }
            ],
            "last_updated": datetime.datetime.now().isoformat(),
            "version": "1.0",
            "admin_settings": {
                "data_url": "https://raw.githubusercontent.com/username/repo/main/data.json",
                "admin_code": "RAYAA2024"
            }
        }

        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json")],
            initialvalue="rayaa_channels.json"
        )

        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(rayaa_data, f, ensure_ascii=False, indent=2)

                # تحديث القنوات المحملة
                self.app_channels = rayaa_data["channels"]

                messagebox.showinfo("تم الإنشاء",
                    f"تم إنشاء ملف قنوات راية كافية بنجاح!\n"
                    f"📁 الملف: {file_path}\n"
                    f"📺 عدد القنوات: {len(rayaa_data['channels'])}\n"
                    f"🔑 كود التفعيل: {rayaa_data['daily_code']}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء الملف:\n{e}")

    def copy_html_code(self):
        """نسخ كود HTML إلى الحافظة"""
        if not self.matches:
            messagebox.showwarning("تحذير", "لا توجد مباريات لتوليد الكود!\nيرجى تحميل المباريات أولاً.")
            return

        # تحديث بيانات القنوات من الجدول
        for i, item in enumerate(self.tree.get_children()):
            values = self.tree.item(item, 'values')
            if i < len(self.matches):
                self.matches[i]["original_channel"] = values[3]
                self.matches[i]["app_channel"] = values[4]

        html = self.generate_advanced_html()

        if not PYPERCLIP_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة pyperclip غير مثبتة!\nاستخدم الأمر التالي لتثبيتها:\npip install pyperclip")
            return

        try:
            pyperclip.copy(html)
            messagebox.showinfo("تم النسخ", f"تم نسخ كود HTML متقدم لـ {len(self.matches)} مباراة إلى الحافظة!\n✨ يشمل اللوجوهات والقنوات المربوطة بالتطبيق")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء النسخ:\n{e}")

    def save_debug_html(self):
        """حفظ HTML الخام من الموقع للتحقق من الهيكل"""
        date_str = self.date_entry.get()
        try:
            parsed_date = datetime.datetime.strptime(date_str, "%Y-%m-%d")
            url = f"https://www.yallakora.com/match-center?date={parsed_date.month}/{parsed_date.day}/{parsed_date.year}#days"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers)

            file_path = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML Files", "*.html")],
                initialvalue="yallakora_debug.html"
            )

            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(response.text)
                messagebox.showinfo("تم الحفظ", f"تم حفظ HTML الخام للتحقق!\nيمكنك فتح الملف لرؤية هيكل الصفحة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ HTML:\n{e}")

# تشغيل البرنامج
if __name__ == "__main__":
    root = tk.Tk()
    app = MatchScraperApp(root)
    root.geometry("700x500")
    root.mainloop()
